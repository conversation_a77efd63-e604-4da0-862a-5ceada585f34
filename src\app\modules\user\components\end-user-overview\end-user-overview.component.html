<section *ngIf="!loading">
  <!-- <div class="profile-page">
    <div class="banner">
      <img [src]="selectedImage" />
      <div
        *ngIf="userState$.userType != companyType.EndUser"
        style="
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        "
        class="text-white"
      >
        <span
          role="button"
          (click)="navigateToCompany()"
          class="fs-1 d-flex flex justify-content-center align-items-center"
          tooltip="View {{ vm?.companyName }}"
          style="font-size: 1.5rem; font-weight: 700"
        >
          <i class="fa fa-building fs-3"></i> &nbsp; {{ vm?.companyName }}</span
        >
        <div>
          <p class="mt-2" style="font-weight: 500">
            <span *ngFor="let item of experties | slice : 0 : 3; let i = index">
              <span *ngIf="i !== 0"> {{ "||" }} </span> {{ item.name }}
            </span>
          </p>
        </div>
      </div>
      <div
        *ngIf="userState$.userType == companyType.EndUser"
        style="font-size: 1.5rem; font-weight: 700"
      >
        <i class="fa fa-building fs-3"></i> &nbsp;
        {{ endUser.organizationName }}
      </div>
      <div
        class="user-icon position-absolute"
        style="right: 0rem; bottom: 0rem"
      >
        <button (click)="uploadBanner()" class="btn btn-primary rounded-0">
          <i class="fa fa-pen"></i>
        </button>
        <input
          type="file"
          class="hide d-none"
          accept="image/jpeg, image/png"
          #bannerFile
          (change)="uploadBannerImage($event)"
        />
      </div>
    </div>
    <div class="profile-container">
      <div class="profile-picture">
        <img [src]="vm.profilePhoto" alt="Profile Picture" appImg />
        <span
          class="p-1 position-absolute d-flex justify-content-center"
          (click)="updateProfile()"
          role="button"
          tooltip="Change profile picture"
          style="bottom: 0px; width: 35px; right: 0px; background: white"
        >
          <i class="fa fa-pen"></i>
        </span>
        <input
          type="file"
          accept="image/png, image/jpeg"
          name="profile"
          class="d-none"
          id="profile"
          #profile
          (change)="uploadProfileImage($event)"
        />
      </div>
    </div>
  </div> -->
  

<div class="container-fluid">
  <div class="row">
    <!-- <div class="col-md-6 d-flex">
      <div>
        <span class="fs-24" style="font-size: 1.5rem; font-weight: 700">
          {{ endUser.firstName | titlecase }}
          {{ endUser.lastName | titlecase }}
        </span>
        <p class="fs-14 m-0" tooltip="Locate on map">
          <i class="fa fa-map-marker-alt text-primary"></i> &nbsp;
          {{ endUser.address | titlecase }}
        </p>
        <p class="fs-14 m-0">
          <i class="fa fa-phone text-primary"></i> &nbsp;
          {{ endUser.countryCode }} {{ endUser.phoneNumber | phoneFormat }}
        </p>
        <p class="fs-14 m-0">
          <i class="fa fa-envelope text-primary"></i> &nbsp;
          {{ endUser.email }}
        </p>
      </div>
    </div> -->
    <div class="col-md-6 fs-12">
      <div class="d-flex gap-3 justify-content-end">
        <!-- <div class="social d-flex gap-3">
          <div
            tooltip="Visit"
            class="text-center"
            *ngFor="let item of socialMediaLinks"
          >
            <a>
              <img height="36" width="36" [src]="item.imageUrl" role="button" />
            </a>
          </div>
        </div> -->
        <!-- <div class="d-flex justify-content-around align-items-center gap-3">
          <div role="button" routerLink="/my-account/chat">
            <img src="./assets/svgs/chat.svg" alt="" srcset="" />
          </div>
          <div>
            <ng-container>
              <div
                class="d-flex justify-content-center align-items-center"
                style="
                  height: 36px;
                  width: 36px;
                  border-radius: 8px;
                  background: #ffddda;
                "
                role="button"
              >
                <i class="fa fa-heart text-danger"></i>
              </div>
            </ng-container>
          </div>
          <div role="button" [tooltip]="tooltip">
            <img src="./assets/svgs/share-profile.svg" alt="" srcset="" />
          </div>
          <ng-template #tooltip>
            {{
              !linkedCopied
                ? "Click to copy link"
                : "link copied to you clipboard!"
            }}
          </ng-template>
        </div> -->
      </div>
    </div>
  </div>
  <!-- <hr /> -->

  <!-- <div class="row">
    <div class="col-md-12">
      <h4>About Me</h4>
      <p *ngIf="endUser.aboutMe; else noAboutMeTemplate">
        {{ endUser.aboutMe }}
      </p>
    </div>
  </div> -->
</div>

<!-- <ng-template #noAboutMeTemplate>
  <div class="card card-body text-center d-flex align-items-center">
    <p>You haven't added any details about yourself yet.</p>
    <button
      [routerLink]="'/my-account/user-detail'"
      class="btn btn-outline-primary w-25 btn-sm"
    >
      Add Now
    </button>
  </div>
</ng-template> -->


<div class="fc-expert-overview-wrapper pb-5">
  <div class="fc-full-common-banner">
    <!-- <img src="../../../../../assets/images/comon-image.jpg" alt=""> -->    
    <img [src]="selectedImage" />
    <div
      *ngIf="userState$.userType != companyType.EndUser"
      style="
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      "
      class="text-white"
    >
    </div>
    <div
      *ngIf="userState$.userType == companyType.EndUser"
      style="font-size: 1.5rem; font-weight: 700"
    >
      <i class="fa fa-building fs-3"></i> &nbsp;
      {{ endUser.organizationName }}
    </div>
    <div
      class="user-icon position-absolute"
      style="right: 0rem; top:1rem"
    >
      <button (click)="uploadBanner()" class="change-banner-picture">
        <i class="fa fa-pen"></i>
      </button>
      <input
        type="file"
        class="hide d-none"
        accept="image/jpeg, image/png"
        #bannerFile
        (change)="uploadBannerImage($event)"
      />
    </div>

  </div>
  <div class="fc-user-profle-view">
    <div class="fc-container">
      <div class="fc-top-personal-detail">
        <div class="top-line">
          <div class="fc-user-avtar">
            <div class="user-img">
              <!-- <img src="../../../../../assets/images/create-profile.jpg"> -->
              <div class="profile-picture profile-container">
                <img [src]="vm.profilePhoto" alt="Profile Picture" appImg />
                <span
                  class="p-1 position-absolute d-flex justify-content-center change-profile-picture"
                  (click)="updateProfile()"
                  role="button"
                  tooltip="Change profile picture"
                  style="background: white"
                >
                  <i class="fa fa-pen"></i>
                </span>
                <input
                  type="file"
                  accept="image/png, image/jpeg"
                  name="profile"
                  class="d-none"
                  id="profile"
                  #profile
                  (change)="uploadProfileImage($event)"
                />
              </div>

            </div>
            <div class="fc-user-name">
              <label>{{ endUser.firstName | titlecase }} {{ endUser.lastName | titlecase }}</label>
              <span>
                <b>{{ endUser.organizationName || '-'}}</b>
              </span>
            </div>
          </div>
          <div class="fc-address-detail">
            <div class="fc-text-bar">
              <label>Join Date</label>
              <span>{{endUser.createdAt | date:'MM-dd-yyyy'}}</span>
            </div>
            <div class="fc-text-bar">
              <label>Office number</label>
              <span>{{ endUser.mobileCountryCode }} {{ endUser.phoneNumber | phoneFormat }}</span>
            </div>
            <div class="fc-text-bar">
              <label>Organization Vertical</label>
              <span>{{ endUser.verticalName || '-' }}</span>
            </div>            
        </div>
          <div class="fc-socile-profile">            
            <button class="chat-btn" role="button" routerLink="/my-account/chat">
              <svg width="15" height="17" viewBox="0 0 15 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12.8001 2.19327C9.8759 -0.731221 5.11751 -0.730956 2.19327 2.19327C-0.731089 5.11762 -0.731089 9.87588 2.19327 12.8001C3.82907 14.4362 6.152 15.2256 8.44583 14.9354L11.035 16.8478C11.1216 16.9118 11.2257 16.9453 11.3311 16.9453C11.3754 16.9453 11.4198 16.9394 11.4633 16.9275C11.6107 16.8868 11.7315 16.7809 11.7907 16.6399L12.5292 14.8829C12.6359 14.6291 12.5166 14.3369 12.2628 14.2302C12.0089 14.1237 11.7165 14.2428 11.6101 14.4966L11.1173 15.6691L8.87424 14.0125C8.76727 13.9334 8.63253 13.8997 8.50154 13.9209C6.45331 14.2389 4.35963 13.5567 2.89817 12.0952C0.3626 9.55966 0.3626 5.43385 2.89817 2.89815C5.4336 0.362715 9.55928 0.362467 12.0952 2.89815C13.7066 4.50981 14.3514 6.79743 13.82 9.01753C13.8187 9.02299 13.8174 9.02843 13.8163 9.03389C13.7418 9.3409 13.6446 9.64403 13.53 9.92831L12.3654 12.6994C12.2587 12.9532 12.378 13.2454 12.6318 13.3521C12.8856 13.4587 13.1779 13.3395 13.2845 13.0857L14.4518 10.308C14.5881 9.97025 14.7008 9.61845 14.7816 9.28262C14.789 9.25913 14.7946 9.23549 14.7985 9.21173C15.3949 6.66372 14.6495 4.04263 12.8001 2.19327Z" fill="white"/>
                <path d="M10.6573 5.16162H4.6527C4.43241 5.16162 4.25391 5.33837 4.25391 5.55866C4.25391 5.77896 4.43241 5.95571 4.6527 5.95571H10.6573C10.8776 5.95571 11.0561 5.77896 11.0561 5.55866C11.0561 5.33837 10.8776 5.16162 10.6573 5.16162Z" fill="white"/>
                <path d="M11.0561 7.84138C11.0561 7.62109 10.8776 7.44434 10.6573 7.44434H4.6527C4.43241 7.44434 4.25391 7.62109 4.25391 7.84138C4.25391 8.06167 4.43241 8.23842 4.6527 8.23842H10.6573C10.8776 8.23842 11.0561 8.06167 11.0561 7.84138Z" fill="white"/>
                <path d="M4.6527 9.72803C4.43241 9.72803 4.25391 9.90478 4.25391 10.1251C4.25391 10.3454 4.43241 10.5221 4.6527 10.5221H8.08529C8.30558 10.5221 8.48409 10.3454 8.48409 10.1251C8.48409 9.90478 8.30558 9.72803 8.08529 9.72803H4.6527Z" fill="white"/>
                </svg>                
            </button>
            <div class="share-btn" role="button" [tooltip]="tooltip" (click)="copyLink()">
              <svg width="14" height="16" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M11.5572 15.1391C10.9601 15.1391 10.4525 14.9301 10.0345 14.5121C9.61649 14.0941 9.40749 13.5865 9.40749 12.9894C9.40749 12.9177 9.4254 12.7505 9.46123 12.4878L4.4273 9.54983C4.23621 9.72897 4.01526 9.8693 3.76446 9.97081C3.51366 10.0723 3.24495 10.1231 2.95832 10.1231C2.36117 10.1231 1.8536 9.91408 1.4356 9.49608C1.01759 9.07808 0.808594 8.57051 0.808594 7.97336C0.808594 7.37622 1.01759 6.86864 1.4356 6.45064C1.8536 6.03264 2.36117 5.82364 2.95832 5.82364C3.24495 5.82364 3.51366 5.8744 3.76446 5.97591C4.01526 6.07743 4.23621 6.21775 4.4273 6.3969L9.46123 3.45894C9.43735 3.37534 9.42242 3.29473 9.41645 3.2171C9.41047 3.13947 9.40749 3.05288 9.40749 2.95734C9.40749 2.3602 9.61649 1.85262 10.0345 1.43462C10.4525 1.01662 10.9601 0.807617 11.5572 0.807617C12.1544 0.807617 12.6619 1.01662 13.0799 1.43462C13.4979 1.85262 13.7069 2.3602 13.7069 2.95734C13.7069 3.55449 13.4979 4.06206 13.0799 4.48006C12.6619 4.89806 12.1544 5.10706 11.5572 5.10706C11.2706 5.10706 11.0019 5.05631 10.7511 4.95479C10.5003 4.85328 10.2793 4.71295 10.0882 4.5338L5.0543 7.47176C5.07818 7.55536 5.09311 7.63597 5.09908 7.7136C5.10506 7.79123 5.10804 7.87782 5.10804 7.97336C5.10804 8.06891 5.10506 8.15549 5.09908 8.23312C5.09311 8.31075 5.07818 8.39136 5.0543 8.47496L10.0882 11.4129C10.2793 11.2338 10.5003 11.0934 10.7511 10.9919C11.0019 10.8904 11.2706 10.8397 11.5572 10.8397C12.1544 10.8397 12.6619 11.0487 13.0799 11.4667C13.4979 11.8847 13.7069 12.3922 13.7069 12.9894C13.7069 13.5865 13.4979 14.0941 13.0799 14.5121C12.6619 14.9301 12.1544 15.1391 11.5572 15.1391ZM11.5572 13.706C11.7602 13.706 11.9304 13.6373 12.0678 13.4999C12.2051 13.3626 12.2738 13.1924 12.2738 12.9894C12.2738 12.7864 12.2051 12.6162 12.0678 12.4788C11.9304 12.3415 11.7602 12.2728 11.5572 12.2728C11.3542 12.2728 11.184 12.3415 11.0467 12.4788C10.9093 12.6162 10.8406 12.7864 10.8406 12.9894C10.8406 13.1924 10.9093 13.3626 11.0467 13.4999C11.184 13.6373 11.3542 13.706 11.5572 13.706ZM2.95832 8.68994C3.16135 8.68994 3.33153 8.62126 3.46888 8.48392C3.60622 8.34658 3.67489 8.17639 3.67489 7.97336C3.67489 7.77033 3.60622 7.60015 3.46888 7.4628C3.33153 7.32546 3.16135 7.25679 2.95832 7.25679C2.75529 7.25679 2.5851 7.32546 2.44776 7.4628C2.31041 7.60015 2.24174 7.77033 2.24174 7.97336C2.24174 8.17639 2.31041 8.34658 2.44776 8.48392C2.5851 8.62126 2.75529 8.68994 2.95832 8.68994ZM11.5572 3.67392C11.7602 3.67392 11.9304 3.60524 12.0678 3.4679C12.2051 3.33056 12.2738 3.16037 12.2738 2.95734C12.2738 2.75431 12.2051 2.58412 12.0678 2.44678C11.9304 2.30944 11.7602 2.24077 11.5572 2.24077C11.3542 2.24077 11.184 2.30944 11.0467 2.44678C10.9093 2.58412 10.8406 2.75431 10.8406 2.95734C10.8406 3.16037 10.9093 3.33056 11.0467 3.4679C11.184 3.60524 11.3542 3.67392 11.5572 3.67392Z"
                  fill="white" />
              </svg>
            </div>
            <ng-template #tooltip>
              {{
                !linkedCopied
                  ? "Click to copy link"
                  : "link copied to you clipboard!"
              }}
            </ng-template>
          </div>
        </div>

      </div>
    </div>
  </div>
  <div class="fc-container">
    <div class="fc-proile-tab-section">
      <div class="fc-left-bar">
        <div class="fc-card d-flex flex-column gap-2">
          <div class="text-up">
            <span><svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M6 7C6.69223 7 7.36892 6.79473 7.9445 6.41015C8.52007 6.02556 8.96867 5.47894 9.23358 4.83939C9.49849 4.19985 9.5678 3.49612 9.43275 2.81719C9.2977 2.13825 8.96436 1.51461 8.47487 1.02513C7.98539 0.535644 7.36175 0.202301 6.68282 0.0672531C6.00388 -0.0677952 5.30015 0.00151649 4.66061 0.266423C4.02107 0.53133 3.47444 0.979934 3.08986 1.55551C2.70527 2.13108 2.5 2.80777 2.5 3.5C2.5 4.42826 2.86875 5.3185 3.52513 5.97488C4.1815 6.63125 5.07174 7 6 7ZM6 1C6.49445 1 6.9778 1.14662 7.38893 1.42133C7.80005 1.69603 8.12048 2.08648 8.3097 2.54329C8.49892 3.00011 8.54843 3.50278 8.45196 3.98773C8.3555 4.47268 8.1174 4.91814 7.76777 5.26777C7.41814 5.6174 6.97268 5.8555 6.48773 5.95197C6.00277 6.04843 5.50011 5.99892 5.04329 5.8097C4.58648 5.62048 4.19603 5.30005 3.92133 4.88893C3.64662 4.4778 3.5 3.99446 3.5 3.5C3.5 2.83696 3.76339 2.20108 4.23223 1.73223C4.70107 1.26339 5.33696 1 6 1Z" fill="#014681"/>
              <path d="M6.5 8H5.5C4.04131 8 2.64236 8.57946 1.61091 9.61091C0.579463 10.6424 0 12.0413 0 13.5C0 13.6326 0.0526785 13.7598 0.146447 13.8536C0.240215 13.9473 0.367392 14 0.5 14H11.5C11.6326 14 11.7598 13.9473 11.8536 13.8536C11.9473 13.7598 12 13.6326 12 13.5C12 12.0413 11.4205 10.6424 10.3891 9.61091C9.35764 8.57946 7.95869 8 6.5 8ZM1.03 13C1.15295 11.9003 1.67676 10.8845 2.50134 10.1466C3.32592 9.40873 4.39347 9.00053 5.5 9H6.5C7.60653 9.00053 8.67408 9.40873 9.49866 10.1466C10.3232 10.8845 10.8471 11.9003 10.97 13H1.03Z" fill="#014681"/>
              </svg>
              </span>
              <label>Owner at {{ endUser.organizationName }}</label>
          </div>
          
          <div class="text-up">
            <span>
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_907_4297)">
                <path d="M8.00018 16.0003C7.88687 16.0008 7.77456 15.979 7.66968 15.9361C7.5648 15.8932 7.4694 15.8301 7.38893 15.7503L3.27893 11.6403C2.63 10.995 2.11633 10.2267 1.76797 9.38035C1.41962 8.53404 1.24359 7.62674 1.25018 6.71157C1.24738 5.8255 1.42139 4.94778 1.76202 4.1298C2.10265 3.31182 2.60305 2.57001 3.23393 1.94782C4.50808 0.702865 6.21878 0.00585937 8.00018 0.00585938C9.78158 0.00585938 11.4923 0.702865 12.7664 1.94782C13.3973 2.57001 13.8977 3.31182 14.2383 4.1298C14.579 4.94778 14.753 5.8255 14.7502 6.71157C14.7568 7.62674 14.5807 8.53404 14.2324 9.38035C13.884 10.2267 13.3704 10.995 12.7214 11.6403L8.61143 15.7428C8.53158 15.824 8.43646 15.8885 8.33155 15.9327C8.22664 15.9769 8.11402 15.9999 8.00018 16.0003ZM8.00018 0.750321C6.41403 0.746668 4.89046 1.36876 3.76018 2.48157C3.20023 3.03441 2.75616 3.69337 2.45395 4.41991C2.15175 5.14644 1.99748 5.92595 2.00018 6.71282C1.99418 7.52906 2.15106 8.33831 2.46163 9.09318C2.77221 9.84805 3.23024 10.5334 3.80893 11.1091L7.91768 15.2116C7.92829 15.2226 7.94101 15.2313 7.95507 15.2373C7.96914 15.2433 7.98427 15.2464 7.99955 15.2464C8.01484 15.2464 8.02996 15.2433 8.04403 15.2373C8.0581 15.2313 8.07082 15.2226 8.08143 15.2116L12.1914 11.1091C12.7701 10.5334 13.2281 9.84805 13.5387 9.09318C13.8493 8.33831 14.0062 7.52906 14.0002 6.71282C14.0029 5.92595 13.8486 5.14644 13.5464 4.41991C13.2442 3.69337 12.8001 3.03441 12.2402 2.48157C11.1099 1.36876 9.58632 0.746668 8.00018 0.750321Z" fill="#014681"/>
                <path d="M8.00037 10.1758C7.21253 10.1754 6.44917 9.90196 5.8403 9.40198C5.23143 8.902 4.81471 8.20642 4.66109 7.4337C4.50747 6.66098 4.62646 5.8589 4.9978 5.16405C5.36914 4.46921 5.96986 3.92457 6.69765 3.62289C7.42545 3.32122 8.23532 3.28115 8.98934 3.50953C9.74336 3.7379 10.3949 4.22059 10.833 4.8754C11.2711 5.5302 11.4687 6.31662 11.3921 7.10073C11.3155 7.88484 10.9694 8.61815 10.4129 9.17578C10.0964 9.49308 9.72034 9.74476 9.30634 9.91636C8.89234 10.088 8.44853 10.1761 8.00037 10.1758ZM8.00037 4.11453C7.38567 4.11551 6.79033 4.32955 6.31572 4.7202C5.84112 5.11084 5.51662 5.65394 5.39748 6.25698C5.27835 6.86003 5.37195 7.48572 5.66235 8.0275C5.95275 8.56927 6.42198 8.99363 6.99013 9.22828C7.55828 9.46293 8.1902 9.49337 8.77827 9.31442C9.36634 9.13546 9.87419 8.75817 10.2153 8.24682C10.5564 7.73546 10.7098 7.12166 10.6491 6.50996C10.5885 5.89826 10.3177 5.32649 9.88287 4.89203C9.63567 4.64496 9.34214 4.4491 9.0191 4.31568C8.69607 4.18226 8.34988 4.11391 8.00037 4.11453Z" fill="#014681"/>
                </g>
                <defs>
                <clipPath id="clip0_907_4297">
                <rect width="16" height="16" fill="white"/>
                </clipPath>
                </defs>
                </svg>                          
              </span>
              <label>{{ endUser.address | titlecase }}</label>
          </div>  

          <div class="text-up">
            <span><svg width="16" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M13.6118 13.0002H2.78823C1.8 13.0002 1 12.1968 1 11.2045V3.50179C1 2.50942 1.8 1.70605 2.78823 1.70605H13.6118C14.6 1.70605 15.4 2.50942 15.4 3.50179V11.2045C15.4471 12.1968 14.6 13.0002 13.6118 13.0002Z" stroke="#014681" stroke-miterlimit="10" stroke-linecap="round"/>
              <path d="M7.99912 8.06958L1.22266 2.48242" stroke="#014681" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M14.7765 2.48242L8 8.06958" stroke="#014681" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>            
              </span><label> {{ endUser.email }}  </label>
          </div>  
        </div>

        <div class="fc-card p-0 pt-3 company-offer-card">
          <label class="fc-company-offer-hdr mb-3">Company Offering</label>

          <!-- Debug information -->
          <!-- <div style="background: #e0e0e0; padding: 10px; margin: 10px 0; border: 1px solid #999;">
            <strong>Debug Info:</strong><br>
            userState$.userType: {{ userState$.userType }}<br>
            companyType.EndUser: {{ companyType.EndUser }}<br>
            endUserExpertiseData: {{ endUserExpertiseData | json }}
          </div> -->

          <!-- <app-expertise
            *ngIf="userState$.userType != companyType.EndUser"
            [companyId]="vm.expertDetail.companyId">
          </app-expertise> -->
          <app-expertise
            *ngIf="userState$.userType == companyType.EndUser"
            [isEndUser]="true"
            [endUserData]="endUserExpertiseData">
          </app-expertise>
        </div>
            
      </div>
      <div class="fc-center-bar">
        <div class="tab-content">
          <div class="fc-card">
            <div class="about-us-card">
              <label>About</label>
              <p *ngIf="endUser.aboutMe; else noAboutMeTemplate">
                {{ endUser.aboutMe }}
              </p>
            </div>
            <ng-template #noAboutMeTemplate>
              <div class="card card-body text-center d-flex align-items-center">
                <p>You haven't added any details about yourself yet.</p>
                <button [routerLink]="'/my-account/user-detail'" class="btn btn-outline-primary w-25 btn-sm">
                  Add Now
                </button>
              </div>
            </ng-template>
          </div>

 <!-- Connections and Referrals Section -->


  <div class="mt-4" *ngIf="showAllConnections">
  <span class="back-icon" (click)="back()" role="button"><img src="../../../../../assets/images/arrow_back.svg">
    Back</span>
  </div>

 <div class="fc-card connectionsAndReferrals-list-here mt-2" *ngIf="showAllConnections" >
  <div class="fc-connection-card-header">
    <h6 class="pt-3 mb-0 fw-semibold">Connections and Referrals</h6>
    <label class="fs-12 mb-0">Only you can see this lead section</label>      
  </div>
  <div class="list-of-connection" *ngFor="let connection of connectionsAndReferrals">
    <div class="ref-profile">
      <!-- <img [src]="connection.profilePhoto || './assets/images/create-profile.jpg'"> -->
      <img 
      [src]="connection.profilePhoto ? connection.profilePhoto : 'assets/images/create-profile.jpg'" 
      alt="Company Profile Photo" 
      onerror="this.src='assets/images/create-profile.jpg'" /> 
    </div>
    <div class="refer-name">
      <label>{{ connection.name }}</label>
      <div class="refer-post">
        <b>{{ connection.companyName }}</b> | <span class="single-expertise">{{ connection.expertise }}</span>
      </div>
    </div>
    <div class="fc-connect-user">
      <button class="add-connection-btn">
        <ng-container
        *ngIf="connection.isCompleted"
      >
        <span class="request-accepted-btn">
          <svg width="23" height="22" viewBox="0 0 23 22" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M11.2528 14.4374C11.1623 14.4379 11.0726 14.4206 10.9889 14.3864C10.9051 14.3521 10.8289 14.3017 10.7647 14.238L8.01469 11.488C7.88523 11.3585 7.8125 11.183 7.8125 10.9999C7.8125 10.8168 7.88523 10.6412 8.01469 10.5118C8.14415 10.3823 8.31973 10.3096 8.50281 10.3096C8.6859 10.3096 8.86148 10.3823 8.99094 10.5118L11.2528 12.7805L15.5772 8.44926C15.7066 8.3198 15.8822 8.24707 16.0653 8.24707C16.2484 8.24707 16.424 8.3198 16.5534 8.44926C16.6829 8.57872 16.7556 8.7543 16.7556 8.93738C16.7556 9.12047 16.6829 9.29605 16.5534 9.42551L11.7409 14.238C11.6767 14.3017 11.6005 14.3521 11.5167 14.3864C11.433 14.4206 11.3433 14.4379 11.2528 14.4374Z" fill="#8A9A5B"/>
            <path d="M11.9395 19.9375C10.1718 19.9375 8.44381 19.4133 6.97405 18.4313C5.50428 17.4492 4.35874 16.0534 3.68228 14.4202C3.00583 12.7871 2.82883 10.9901 3.17369 9.25638C3.51854 7.52268 4.36976 5.93017 5.61969 4.68024C6.86962 3.43031 8.46213 2.57909 10.1958 2.23424C11.9295 1.88938 13.7266 2.06637 15.3597 2.74283C16.9928 3.41929 18.3887 4.56483 19.3707 6.0346C20.3528 7.50436 20.877 9.23233 20.877 11C20.877 13.3704 19.9353 15.6437 18.2592 17.3198C16.5831 18.9959 14.3098 19.9375 11.9395 19.9375ZM11.9395 3.4375C10.4437 3.4375 8.9816 3.88104 7.73796 4.71201C6.49431 5.54299 5.52501 6.72409 4.95262 8.10596C4.38023 9.48783 4.23047 11.0084 4.52227 12.4754C4.81407 13.9424 5.53433 15.2899 6.59196 16.3475C7.6496 17.4051 8.99711 18.1254 10.4641 18.4172C11.9311 18.709 13.4516 18.5592 14.8335 17.9868C16.2154 17.4145 17.3965 16.4452 18.2274 15.2015C19.0584 13.9579 19.502 12.4957 19.502 11C19.502 8.9943 18.7052 7.07075 17.287 5.65251C15.8687 4.23427 13.9452 3.4375 11.9395 3.4375Z" fill="#8A9A5B"/>
            </svg>                      
        </span>
        </ng-container>
        <ng-container
        *ngIf="!connection.isRequestReceived && !connection.isRequestSent && !connection.isCompleted"
        >
        <span class="connect-request-btn" (click)="connectUser(connection)">
          <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10.0061 14.167H11.6793V10.8337H15.0258V9.16699H11.6793V5.83366H10.0061V9.16699H6.65963V10.8337H10.0061V14.167ZM10.8427 18.3337C9.68539 18.3337 8.59779 18.1149 7.57991 17.6774C6.56203 17.2399 5.67661 16.6462 4.92366 15.8962C4.17071 15.1462 3.57462 14.2642 3.1354 13.2503C2.69617 12.2364 2.47656 11.1531 2.47656 10.0003C2.47656 8.84755 2.69617 7.76421 3.1354 6.75033C3.57462 5.73644 4.17071 4.85449 4.92366 4.10449C5.67661 3.35449 6.56203 2.76074 7.57991 2.32324C8.59779 1.88574 9.68539 1.66699 10.8427 1.66699C12 1.66699 13.0876 1.88574 14.1055 2.32324C15.1234 2.76074 16.0088 3.35449 16.7617 4.10449C17.5147 4.85449 18.1108 5.73644 18.55 6.75033C18.9892 7.76421 19.2088 8.84755 19.2088 10.0003C19.2088 11.1531 18.9892 12.2364 18.55 13.2503C18.1108 14.2642 17.5147 15.1462 16.7617 15.8962C16.0088 16.6462 15.1234 17.2399 14.1055 17.6774C13.0876 18.1149 12 18.3337 10.8427 18.3337ZM10.8427 16.667C12.7111 16.667 14.2937 16.0212 15.5905 14.7295C16.8872 13.4378 17.5356 11.8614 17.5356 10.0003C17.5356 8.13921 16.8872 6.56283 15.5905 5.27116C14.2937 3.97949 12.7111 3.33366 10.8427 3.33366C8.97427 3.33366 7.39167 3.97949 6.09492 5.27116C4.79817 6.56283 4.14979 8.13921 4.14979 10.0003C4.14979 11.8614 4.79817 13.4378 6.09492 14.7295C7.39167 16.0212 8.97427 16.667 10.8427 16.667Z" fill="#014681"/>
          </svg>                    
        </span>
        </ng-container>
      </button>
    </div>
  </div>
</div> 

        </div>
            <!-- Service Activities Section -->
            <div class="fc-card">
              <label class="fw-semibold fs-6 mb-2">Service activities</label>
              <app-service-activities                 
                [isLoggedIn]="!!userState$.userId"
                [currentUserId]="userState$.userId"
                [userId]="vm.userId"
                [userProfilePhoto]="vm.profilePhoto"
                [userName]="vm.firstName + ' ' + vm.lastName" [userRole]="vm.expertDetail?.roleName || 'End User'"
                
                [currentUserName]="userState$.firstName + ' ' + userState$.lastName"
                [currentUserFirstName]="userState$.firstName"
                [currentUserLastName]="userState$.lastName">
              </app-service-activities>
            </div>
      </div>
      <div class="fc-right-bar">    
        <div class="fc-card p-0" *ngIf="!showAllConnections">
          <div class="fc-connection-card" [ngClass]="{'pb-3': (connectionsAndReferrals | slice: 0:3).length < 3}">
            <div class="fc-connection-card-header">
              <h4 class="pt-3 mb-0 fw-semibold">Connections and Referrals</h4>
              <label class="fs-12 mb-0">Only you can see this lead section</label>      
            </div>
            <div class="list-of-connection" *ngFor="let connection of connectionsAndReferrals | slice: 0:3">
              <div class="ref-profile" role="button" (click)="navigate(connection)">
                <!-- <img [src]="connection.profilePhoto || './assets/images/create-profile.jpg'"> -->
                <img [src]="connection.profilePhoto ? connection.profilePhoto : 'assets/images/create-profile.jpg'"
                            alt="Company Profile Photo" onerror="this.src='assets/images/create-profile.jpg'" />
              </div>
              <div class="refer-name" (click)="navigate(connection)" role="button">
                <label>{{ connection.name }}</label>
                <div class="refer-post"><b>{{ connection.companyName }}</b> | <span class="single-expertise">{{ connection.expertise }}</span></div>
              </div>  
              <div class="fc-connect-user">
                <button class="add-connection-btn">
                  <ng-container
                  *ngIf="connection.isCompleted"
                >
                  <span class="request-accepted-btn">
                    <svg width="23" height="22" viewBox="0 0 23 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M11.2528 14.4374C11.1623 14.4379 11.0726 14.4206 10.9889 14.3864C10.9051 14.3521 10.8289 14.3017 10.7647 14.238L8.01469 11.488C7.88523 11.3585 7.8125 11.183 7.8125 10.9999C7.8125 10.8168 7.88523 10.6412 8.01469 10.5118C8.14415 10.3823 8.31973 10.3096 8.50281 10.3096C8.6859 10.3096 8.86148 10.3823 8.99094 10.5118L11.2528 12.7805L15.5772 8.44926C15.7066 8.3198 15.8822 8.24707 16.0653 8.24707C16.2484 8.24707 16.424 8.3198 16.5534 8.44926C16.6829 8.57872 16.7556 8.7543 16.7556 8.93738C16.7556 9.12047 16.6829 9.29605 16.5534 9.42551L11.7409 14.238C11.6767 14.3017 11.6005 14.3521 11.5167 14.3864C11.433 14.4206 11.3433 14.4379 11.2528 14.4374Z" fill="#8A9A5B"/>
                      <path d="M11.9395 19.9375C10.1718 19.9375 8.44381 19.4133 6.97405 18.4313C5.50428 17.4492 4.35874 16.0534 3.68228 14.4202C3.00583 12.7871 2.82883 10.9901 3.17369 9.25638C3.51854 7.52268 4.36976 5.93017 5.61969 4.68024C6.86962 3.43031 8.46213 2.57909 10.1958 2.23424C11.9295 1.88938 13.7266 2.06637 15.3597 2.74283C16.9928 3.41929 18.3887 4.56483 19.3707 6.0346C20.3528 7.50436 20.877 9.23233 20.877 11C20.877 13.3704 19.9353 15.6437 18.2592 17.3198C16.5831 18.9959 14.3098 19.9375 11.9395 19.9375ZM11.9395 3.4375C10.4437 3.4375 8.9816 3.88104 7.73796 4.71201C6.49431 5.54299 5.52501 6.72409 4.95262 8.10596C4.38023 9.48783 4.23047 11.0084 4.52227 12.4754C4.81407 13.9424 5.53433 15.2899 6.59196 16.3475C7.6496 17.4051 8.99711 18.1254 10.4641 18.4172C11.9311 18.709 13.4516 18.5592 14.8335 17.9868C16.2154 17.4145 17.3965 16.4452 18.2274 15.2015C19.0584 13.9579 19.502 12.4957 19.502 11C19.502 8.9943 18.7052 7.07075 17.287 5.65251C15.8687 4.23427 13.9452 3.4375 11.9395 3.4375Z" fill="#8A9A5B"/>
                      </svg>                      
                  </span>
                  </ng-container>
                  <ng-container
                  *ngIf="!connection.isRequestReceived && !connection.isRequestSent && !connection.isCompleted"
                  >
                  <span class="connect-request-btn" (click)="connectUser(connection)">
                    <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M10.0061 14.167H11.6793V10.8337H15.0258V9.16699H11.6793V5.83366H10.0061V9.16699H6.65963V10.8337H10.0061V14.167ZM10.8427 18.3337C9.68539 18.3337 8.59779 18.1149 7.57991 17.6774C6.56203 17.2399 5.67661 16.6462 4.92366 15.8962C4.17071 15.1462 3.57462 14.2642 3.1354 13.2503C2.69617 12.2364 2.47656 11.1531 2.47656 10.0003C2.47656 8.84755 2.69617 7.76421 3.1354 6.75033C3.57462 5.73644 4.17071 4.85449 4.92366 4.10449C5.67661 3.35449 6.56203 2.76074 7.57991 2.32324C8.59779 1.88574 9.68539 1.66699 10.8427 1.66699C12 1.66699 13.0876 1.88574 14.1055 2.32324C15.1234 2.76074 16.0088 3.35449 16.7617 4.10449C17.5147 4.85449 18.1108 5.73644 18.55 6.75033C18.9892 7.76421 19.2088 8.84755 19.2088 10.0003C19.2088 11.1531 18.9892 12.2364 18.55 13.2503C18.1108 14.2642 17.5147 15.1462 16.7617 15.8962C16.0088 16.6462 15.1234 17.2399 14.1055 17.6774C13.0876 18.1149 12 18.3337 10.8427 18.3337ZM10.8427 16.667C12.7111 16.667 14.2937 16.0212 15.5905 14.7295C16.8872 13.4378 17.5356 11.8614 17.5356 10.0003C17.5356 8.13921 16.8872 6.56283 15.5905 5.27116C14.2937 3.97949 12.7111 3.33366 10.8427 3.33366C8.97427 3.33366 7.39167 3.97949 6.09492 5.27116C4.79817 6.56283 4.14979 8.13921 4.14979 10.0003C4.14979 11.8614 4.79817 13.4378 6.09492 14.7295C7.39167 16.0212 8.97427 16.667 10.8427 16.667Z" fill="#014681"/>
                    </svg>                    
                  </span>
                  </ng-container>
                </button>
              </div>
            </div>
            <div class="see-all-bar" role="button" *ngIf="connectionsAndReferrals.length > 3" (click)="showAllConnections = true">
              See All
            </div>
          </div>
        </div>  

        <div class="fc-card">
          <div class="fc-my-expertise">
            <div class="heading-label">
              <label class="fw-semibold">My Expertise</label>
              <p>Please reach out if you are looking for:</p>
            </div>
            <div class="fc-expertise-tag">
              <!-- For End Users: Use endUserExpertiseData -->
              <ul *ngIf="userState$.userType == companyType.EndUser && endUserExpertiseData?.expertise?.length > 0; else checkExpertExpertise">
                <li *ngFor="let expertise of showAllExpertise ? endUserExpertiseData.expertise : (endUserExpertiseData.expertise | slice: 0:3)">
                  #{{ expertise }}
                </li>
              </ul>

              <!-- For Experts: Use existing experties array -->
              <ng-template #checkExpertExpertise>
                <ul *ngIf="experties.length > 0; else noExpertise">
                  <li *ngFor="let expertise of showAllExpertise ? experties : (experties | slice: 0:3)">
                    #{{ expertise.name }}
                  </li>
                </ul>
              </ng-template>

              <ng-template #noExpertise>
                <p>No expertise available at the moment.</p>
              </ng-template>

              <!-- Show All button for End Users -->
              <button *ngIf="userState$.userType == companyType.EndUser && endUserExpertiseData?.expertise?.length > 2"
                      (click)="toggleShowAll()" class="see-all-bar pt-3 h-auto">
                {{ showAllExpertise ? 'Show Less' : 'Show All' }}
              </button>

              <!-- Show All button for Experts -->
              <button *ngIf="userState$.userType != companyType.EndUser && experties.length > 2"
                      (click)="toggleShowAll()" class="see-all-bar pt-3 h-auto">
                {{ showAllExpertise ? 'Show Less' : 'Show All' }}
              </button>
            </div>
          </div>
        </div>

        <div class="fc-card">
          <h6 class="fs-6 mb-0 fw-semibold pb-3">My Social</h6>
          <div class="fs-social-follow">
            <div
            tooltip="Visit"
            class="text-center"
            *ngFor="let item of socialMediaLinks"
          >
            <a [href]="item.url" target="_blank">
              <img height="36" width="36" [src]="item.imageUrl" role="button" />
            </a>
          </div>

          </div>          
        </div>
      </div>
      </div>
    </div>
  </div>
